<mxfile host="app.diagrams.net" modified="2024-06-24T00:00:00.000Z" agent="5.0" etag="xxx" version="21.1.2" type="device">
  <diagram name="Lingx插件运行流程" id="lingx-plugin-flow">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 用户请求 -->
        <mxCell id="user-request" value="用户请求" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontStyle=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="520" y="40" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 插件类型判断 -->
        <mxCell id="plugin-type" value="插件类型判断" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="140" width="160" height="80" as="geometry" />
        </mxCell>
        
        <!-- CHAT插件分支 -->
        <mxCell id="plugin-manager" value="PluginManager" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="280" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="get-plugin-meta" value="获取插件元数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="280" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="plugin-service" value="PluginService&#xa;getPluginCodeById" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="280" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- FLOW插件分支 -->
        <mxCell id="flow-engine" value="FlowEngine" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="760" y="280" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="flow-executor" value="FlowExecutor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="760" y="380" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="job-instance" value="获取JobInstance" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="760" y="480" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 沙箱编译层 -->
        <mxCell id="create-sandbox" value="创建Sandbox实例" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="580" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="compile-plugin" value="Sandbox&#xa;compilePlugin" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="520" y="680" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="vm-isolate" value="创建VM隔离环境" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="360" y="780" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="compile-sdk" value="编译SDK模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="520" y="780" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="compile-types" value="编译类型模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="680" y="780" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 执行层 -->
        <mxCell id="plugin-executor" value="PluginExecutor&#xa;beginExecute" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="880" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="get-lock" value="获取执行锁" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="360" y="980" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="prepare-context" value="准备执行上下文" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="520" y="980" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="call-plugin" value="调用插件方法" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="680" y="980" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 插件方法分支 -->
        <mxCell id="plugin-method-type" value="插件方法类型" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="500" y="1080" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="normal-methods" value="Normal插件&#xa;messageReceived/update" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;" vertex="1" parent="1">
          <mxGeometry x="280" y="1200" width="140" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="job-methods" value="Job插件&#xa;update/watch方法" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="740" y="1200" width="140" height="60" as="geometry" />
        </mxCell>
        
        <!-- 处理逻辑 -->
        <mxCell id="process-logic" value="处理插件逻辑" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="1300" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="update-store" value="更新Store状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="360" y="1400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="update-output" value="更新Output输出" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="520" y="1400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="trigger-events" value="触发事件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;" vertex="1" parent="1">
          <mxGeometry x="680" y="1400" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 通信机制 -->
        <mxCell id="message-queue" value="MessageQueue&#xa;发布事件" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="520" y="1500" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="redis-queue" value="Redis消息队列" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="360" y="1600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="websocket" value="WebSocket推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="520" y="1600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="frontend-update" value="前端更新" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="680" y="1600" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- 连接线 -->
        <mxCell id="edge1" edge="1" parent="1" source="user-request" target="plugin-type">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="plugin-type" target="plugin-manager">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge2-label" value="CHAT插件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge2">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="plugin-type" target="flow-engine">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge3-label" value="FLOW插件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge3">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="plugin-manager" target="get-plugin-meta">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="get-plugin-meta" target="plugin-service">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="flow-engine" target="flow-executor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="flow-executor" target="job-instance">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="plugin-service" target="create-sandbox">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="job-instance" target="create-sandbox">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="create-sandbox" target="compile-plugin">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge11" edge="1" parent="1" source="compile-plugin" target="vm-isolate">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge12" edge="1" parent="1" source="compile-plugin" target="compile-sdk">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge13" edge="1" parent="1" source="compile-plugin" target="compile-types">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge14" edge="1" parent="1" source="compile-sdk" target="plugin-executor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge15" edge="1" parent="1" source="plugin-executor" target="get-lock">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge16" edge="1" parent="1" source="plugin-executor" target="prepare-context">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge17" edge="1" parent="1" source="plugin-executor" target="call-plugin">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge18" edge="1" parent="1" source="call-plugin" target="plugin-method-type">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge19" edge="1" parent="1" source="plugin-method-type" target="normal-methods">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge19-label" value="Normal插件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge19">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge20" edge="1" parent="1" source="plugin-method-type" target="job-methods">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge20-label" value="Job插件" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge20">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>
        
        <mxCell id="edge21" edge="1" parent="1" source="normal-methods" target="process-logic">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge22" edge="1" parent="1" source="job-methods" target="process-logic">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge23" edge="1" parent="1" source="process-logic" target="update-store">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge24" edge="1" parent="1" source="process-logic" target="update-output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge25" edge="1" parent="1" source="process-logic" target="trigger-events">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge26" edge="1" parent="1" source="trigger-events" target="message-queue">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge27" edge="1" parent="1" source="message-queue" target="redis-queue">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge28" edge="1" parent="1" source="message-queue" target="websocket">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge29" edge="1" parent="1" source="message-queue" target="frontend-update">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 生命周期管理 -->
        <mxCell id="lifecycle-title" value="生命周期管理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;fontColor=#1b5e20;" vertex="1" parent="1">
          <mxGeometry x="880" y="980" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="execution-status" value="执行状态判断" style="rhombus;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="880" y="1020" width="120" height="80" as="geometry" />
        </mxCell>

        <mxCell id="wait-status" value="标记wait状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="760" y="1140" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="complete-status" value="释放资源" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="890" y="1140" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="error-status" value="错误处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="1020" y="1140" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="save-state" value="保存中间状态" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="760" y="1220" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="destroy-sandbox" value="销毁Sandbox" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="890" y="1220" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="error-log" value="记录错误日志" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="1020" y="1220" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="release-lock" value="释放执行锁" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;" vertex="1" parent="1">
          <mxGeometry x="890" y="1300" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- 插件间通信 -->
        <mxCell id="communication-title" value="插件间通信" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;fontColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="40" y="1300" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="channel-manager" value="ChannelManager" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="40" y="1340" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="serialize-data" value="序列化数据" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="40" y="1420" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="cross-vm" value="跨VM通信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="40" y="1500" width="100" height="50" as="geometry" />
        </mxCell>

        <mxCell id="deserialize-data" value="反序列化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;" vertex="1" parent="1">
          <mxGeometry x="40" y="1580" width="100" height="50" as="geometry" />
        </mxCell>

        <!-- SDK注入 -->
        <mxCell id="sdk-title" value="SDK注入" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;fontColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="880" y="780" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="inject-fetch" value="注入fetch/logger" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="880" y="820" width="110" height="40" as="geometry" />
        </mxCell>

        <mxCell id="inject-notify" value="注入notify/getUsers" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="880" y="880" width="110" height="40" as="geometry" />
        </mxCell>

        <mxCell id="inject-env" value="注入环境变量" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="1020" y="820" width="100" height="40" as="geometry" />
        </mxCell>

        <mxCell id="inject-message" value="注入消息处理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;" vertex="1" parent="1">
          <mxGeometry x="1020" y="880" width="100" height="40" as="geometry" />
        </mxCell>

        <!-- 生命周期连接线 -->
        <mxCell id="edge30" edge="1" parent="1" source="call-plugin" target="execution-status">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge31" edge="1" parent="1" source="execution-status" target="wait-status">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge31-label" value="等待" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge31">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge32" edge="1" parent="1" source="execution-status" target="complete-status">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge32-label" value="完成" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge32">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge33" edge="1" parent="1" source="execution-status" target="error-status">
          <mxGeometry relative="1" as="geometry" />
          <mxCell id="edge33-label" value="错误" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="edge33">
            <mxGeometry x="-0.1" y="-1" relative="1" as="geometry" />
          </mxCell>
        </mxCell>

        <mxCell id="edge34" edge="1" parent="1" source="wait-status" target="save-state">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge35" edge="1" parent="1" source="complete-status" target="destroy-sandbox">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge36" edge="1" parent="1" source="error-status" target="error-log">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge37" edge="1" parent="1" source="destroy-sandbox" target="release-lock">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge38" edge="1" parent="1" source="error-log" target="release-lock">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 通信连接线 -->
        <mxCell id="edge39" edge="1" parent="1" source="process-logic" target="channel-manager">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge40" edge="1" parent="1" source="channel-manager" target="serialize-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge41" edge="1" parent="1" source="serialize-data" target="cross-vm">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge42" edge="1" parent="1" source="cross-vm" target="deserialize-data">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- SDK注入连接线 -->
        <mxCell id="edge43" edge="1" parent="1" source="compile-types" target="inject-fetch">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge44" edge="1" parent="1" source="compile-types" target="inject-env">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge45" edge="1" parent="1" source="inject-fetch" target="inject-notify">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="edge46" edge="1" parent="1" source="inject-env" target="inject-message">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend-title" value="图例说明" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=18;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="100" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-user" value="用户交互" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1f5fe;strokeColor=#01579b;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="40" y="80" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-plugin-mgr" value="插件管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="40" y="120" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-sandbox" value="沙箱编译" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#e65100;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="40" y="160" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-execution" value="执行层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="40" y="200" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-communication" value="通信机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="40" y="240" width="80" height="30" as="geometry" />
        </mxCell>

        <mxCell id="legend-lifecycle" value="生命周期" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f1f8e9;strokeColor=#33691e;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="140" y="80" width="80" height="30" as="geometry" />
        </mxCell>

        <!-- 关键特性说明 -->
        <mxCell id="features-title" value="关键特性" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1040" y="40" width="100" height="30" as="geometry" />
        </mxCell>

        <mxCell id="feature-isolation" value="🔒 隔离执行&#xa;每个插件在独立VM中运行" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f57f17;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="80" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="feature-security" value="🛡️ 权限控制&#xa;通过SDK限制API访问" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f57f17;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="140" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="feature-async" value="⚡ 异步执行&#xa;支持wait机制和状态恢复" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f57f17;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="200" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="feature-communication" value="📡 实时通信&#xa;WebSocket + MessageQueue" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f57f17;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="260" width="160" height="50" as="geometry" />
        </mxCell>

        <mxCell id="feature-cache" value="🚀 性能优化&#xa;LRU缓存 + 连接池复用" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff9c4;strokeColor=#f57f17;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="320" width="160" height="50" as="geometry" />
        </mxCell>

        <!-- 插件类型对比 -->
        <mxCell id="plugin-types-title" value="插件类型对比" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1040" y="400" width="120" height="30" as="geometry" />
        </mxCell>

        <mxCell id="chat-plugin-desc" value="CHAT插件&#xa;• 聊天场景独立执行&#xa;• messageReceived方法&#xa;• 用户交互驱动&#xa;• 支持表单提交" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#4a148c;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="440" width="160" height="80" as="geometry" />
        </mxCell>

        <mxCell id="flow-plugin-desc" value="FLOW插件&#xa;• 工作流中Job节点&#xa;• update/watch方法&#xa;• 流程驱动执行&#xa;• 支持状态监听" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#2e7d32;fontSize=11;align=left;" vertex="1" parent="1">
          <mxGeometry x="1040" y="530" width="160" height="80" as="geometry" />
        </mxCell>

        <!-- 数据流向说明 -->
        <mxCell id="dataflow-title" value="数据流向" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="1040" y="640" width="100" height="30" as="geometry" />
        </mxCell>

        <mxCell id="input-flow" value="Input → Store → Output" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1040" y="680" width="160" height="30" as="geometry" />
        </mxCell>

        <mxCell id="event-flow" value="Event → MessageQueue → WebSocket" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#880e4f;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1040" y="720" width="160" height="30" as="geometry" />
        </mxCell>

        <!-- 标题 -->
        <mxCell id="main-title" value="Lingx 插件运行流程架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=24;fontColor=#1565c0;" vertex="1" parent="1">
          <mxGeometry x="400" y="0" width="360" height="40" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
